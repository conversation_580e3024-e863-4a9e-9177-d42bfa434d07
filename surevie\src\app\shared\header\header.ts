import { Component, OnInit, On<PERSON><PERSON>roy, Inject, PLATFORM_ID } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './header.html',
  styleUrl: './header.scss'
})
export class Header implements OnInit, OnDestroy {
  isAuthenticated = false;
  currentUser: any = null;
  isMobileMenuOpen = false;
  private authSubscription: Subscription = new Subscription();

  constructor(
    @Inject(PLATFORM_ID) private platformId: Object,
    private router: Router
  ) { }

  ngOnInit(): void {
    // For now, we'll set authentication to false
    // TODO: Re-integrate AuthService once Firebase is properly configured
    this.isAuthenticated = false;
    this.currentUser = null;
  }

  ngOnDestroy(): void {
    this.authSubscription.unsubscribe();
  }

  navigateToLogin(): void {
    this.router.navigate(['/auth/login']);
    this.closeMobileMenu();
  }

  navigateToRegister(): void {
    this.router.navigate(['/auth/register']);
    this.closeMobileMenu();
  }

  logout(): void {
    // TODO: Re-integrate AuthService logout once Firebase is properly configured
    this.router.navigate(['/']);
    this.closeMobileMenu();
  }

  toggleMobileMenu(): void {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
  }

  closeMobileMenu(): void {
    this.isMobileMenuOpen = false;
  }
}
