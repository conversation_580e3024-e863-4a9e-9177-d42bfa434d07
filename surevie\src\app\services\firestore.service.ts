import { Injectable } from '@angular/core';
import { Firestore, collection, doc, addDoc, updateDoc, deleteDoc, getDocs, getDoc, query, where, orderBy } from '@angular/fire/firestore';
import { Observable, from } from 'rxjs';

export interface InsuranceContract {
  id?: string;
  clientId: string;
  brokerId: string;
  contractType: string;
  premium: number;
  coverage: number;
  startDate: Date;
  endDate: Date;
  status: 'active' | 'pending' | 'expired' | 'cancelled';
  createdAt: Date;
  updatedAt: Date;
}

@Injectable({
  providedIn: 'root'
})
export class FirestoreService {

  constructor(private firestore: Firestore) { }

  // Generic methods for any collection
  addDocument(collectionName: string, data: any): Observable<any> {
    const collectionRef = collection(this.firestore, collectionName);
    return from(addDoc(collectionRef, {
      ...data,
      createdAt: new Date(),
      updatedAt: new Date()
    }));
  }

  getDocument(collectionName: string, docId: string): Observable<any> {
    const docRef = doc(this.firestore, collectionName, docId);
    return from(getDoc(docRef));
  }

  updateDocument(collectionName: string, docId: string, data: any): Observable<void> {
    const docRef = doc(this.firestore, collectionName, docId);
    return from(updateDoc(docRef, {
      ...data,
      updatedAt: new Date()
    }));
  }

  deleteDocument(collectionName: string, docId: string): Observable<void> {
    const docRef = doc(this.firestore, collectionName, docId);
    return from(deleteDoc(docRef));
  }

  getCollection(collectionName: string): Observable<any> {
    const collectionRef = collection(this.firestore, collectionName);
    return from(getDocs(collectionRef));
  }

  // Specific methods for insurance contracts
  createContract(contract: Omit<InsuranceContract, 'id'>): Observable<any> {
    return this.addDocument('contracts', contract);
  }

  getContractsByClient(clientId: string): Observable<any> {
    const contractsRef = collection(this.firestore, 'contracts');
    const q = query(contractsRef, where('clientId', '==', clientId), orderBy('createdAt', 'desc'));
    return from(getDocs(q));
  }

  getContractsByBroker(brokerId: string): Observable<any> {
    const contractsRef = collection(this.firestore, 'contracts');
    const q = query(contractsRef, where('brokerId', '==', brokerId), orderBy('createdAt', 'desc'));
    return from(getDocs(q));
  }

  updateContractStatus(contractId: string, status: InsuranceContract['status']): Observable<void> {
    return this.updateDocument('contracts', contractId, { status });
  }
}
