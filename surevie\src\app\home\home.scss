// Vermeg-inspired color palette
$primary-red: #e31e24;
$dark-gray: #333333;
$light-gray: #f8f9fa;
$white: #ffffff;
$border-gray: #e0e0e0;
$text-gray: #666666;
$background-light: #fafbfc;

// Common styles
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.btn {
  padding: 14px 28px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid transparent;
  text-decoration: none;
  display: inline-block;
  text-align: center;
  font-family: inherit;
  position: relative;
  overflow: hidden;
  letter-spacing: 0.5px;

  &.btn-large {
    padding: 18px 36px;
    font-size: 18px;
    font-weight: 700;
    border-radius: 14px;
  }

  &.btn-primary {
    background: linear-gradient(135deg, $primary-red 0%, darken($primary-red, 8%) 100%);
    color: $white;
    border-color: $primary-red;
    box-shadow: 0 4px 15px rgba($primary-red, 0.2);

    &:hover {
      background: linear-gradient(135deg, darken($primary-red, 5%) 0%, darken($primary-red, 15%) 100%);
      border-color: darken($primary-red, 10%);
      transform: translateY(-3px);
      box-shadow: 0 8px 25px rgba($primary-red, 0.35);
    }

    &:active {
      transform: translateY(-1px);
      box-shadow: 0 4px 15px rgba($primary-red, 0.3);
    }
  }

  &.btn-outline {
    background-color: transparent;
    color: $dark-gray;
    border-color: $border-gray;
    backdrop-filter: blur(10px);

    &:hover {
      background-color: $light-gray;
      border-color: $dark-gray;
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba($dark-gray, 0.1);
    }
  }
}

.section-header {
  text-align: center;
  margin-bottom: 60px;

  .section-title {
    font-size: 42px;
    font-weight: 700;
    color: $dark-gray;
    margin-bottom: 16px;
    line-height: 1.2;

    @media (max-width: 768px) {
      font-size: 32px;
    }
  }

  .section-subtitle {
    font-size: 20px;
    color: $text-gray;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;

    @media (max-width: 768px) {
      font-size: 18px;
    }
  }
}

// Hero Section
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #e8f4f8 0%, #d1e7f0 50%, #b8d4e3 100%);
  overflow: hidden;

  // Add subtle animated background elements
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 80%, rgba(227, 30, 36, 0.03) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(227, 30, 36, 0.02) 0%, transparent 50%),
      url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    z-index: 1;
  }

  // Add floating elements for visual interest
  &::after {
    content: '';
    position: absolute;
    top: 10%;
    right: 10%;
    width: 100px;
    height: 100px;
    background: linear-gradient(45deg, rgba($primary-red, 0.05), transparent);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
    z-index: 1;
  }

  .container {
    position: relative;
    z-index: 2;
  }

  .hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
    min-height: 80vh;

    @media (max-width: 968px) {
      grid-template-columns: 1fr;
      gap: 50px;
      text-align: center;
    }
  }

  .hero-text {
    animation: slideInLeft 0.8s ease-out;

    .hero-title {
      font-size: 64px;
      font-weight: 900;
      color: $dark-gray;
      line-height: 1.1;
      margin-bottom: 32px;
      letter-spacing: -0.02em;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

      // Enhanced typography with better spacing
      br {
        @media (max-width: 768px) {
          display: none;
        }
      }

      .highlight {
        color: $primary-red;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: -4px;
          left: 0;
          width: 100%;
          height: 4px;
          background: linear-gradient(90deg, $primary-red, transparent);
          border-radius: 2px;
        }
      }

      @media (max-width: 768px) {
        font-size: 48px;
      }

      @media (max-width: 480px) {
        font-size: 36px;
      }
    }

    .hero-subtitle {
      font-size: 22px;
      color: $text-gray;
      line-height: 1.7;
      margin-bottom: 48px;
      max-width: 520px;
      font-weight: 400;
      opacity: 0.9;

      @media (max-width: 768px) {
        font-size: 20px;
        max-width: none;
        margin-bottom: 40px;
      }
    }

    .hero-actions {
      display: flex;
      gap: 24px;
      margin-bottom: 40px;

      @media (max-width: 480px) {
        flex-direction: column;
        align-items: center;
        gap: 16px;
      }

      .btn-primary {
        position: relative;
        overflow: hidden;
        box-shadow: 0 8px 25px rgba($primary-red, 0.25);
        display: flex;
        align-items: center;
        gap: 8px;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          transition: left 0.5s;
        }

        &:hover::before {
          left: 100%;
        }

        .arrow {
          transition: transform 0.3s ease;
          font-size: 16px;
        }

        &:hover .arrow {
          transform: translateX(4px);
        }
      }
    }

    .hero-features {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-top: 20px;

      @media (max-width: 768px) {
        justify-content: center;
      }

      .feature-badge {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 25px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
        }

        .badge-icon {
          font-size: 16px;
          color: $primary-red;
        }

        .badge-text {
          font-size: 14px;
          font-weight: 600;
          color: $dark-gray;
          white-space: nowrap;
        }
      }
    }

    .hero-note {
      font-size: 16px;
      color: $text-gray;
      font-weight: 500;
    }
  }

  .hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    animation: slideInRight 0.8s ease-out;

    .family-photo {
      position: relative;
      max-width: 100%;
      height: auto;

      // Add decorative elements around the image
      &::before {
        content: '';
        position: absolute;
        top: -20px;
        left: -20px;
        right: -20px;
        bottom: -20px;
        background: linear-gradient(45deg, rgba($primary-red, 0.1), transparent 70%);
        border-radius: 30px;
        z-index: -1;
      }

      &::after {
        content: '';
        position: absolute;
        top: -10px;
        right: -10px;
        width: 60px;
        height: 60px;
        background: $primary-red;
        border-radius: 50%;
        opacity: 0.1;
        animation: pulse 3s infinite;
      }

      .family-img {
        width: 100%;
        max-width: 550px;
        height: auto;
        border-radius: 25px;
        box-shadow:
          0 25px 50px rgba(0, 0, 0, 0.15),
          0 10px 20px rgba(0, 0, 0, 0.1);
        transition: all 0.4s ease;
        border: 3px solid rgba(255, 255, 255, 0.8);

        &:hover {
          transform: scale(1.03) translateY(-5px);
          box-shadow:
            0 35px 70px rgba(0, 0, 0, 0.2),
            0 15px 30px rgba(0, 0, 0, 0.15);
        }

        @media (max-width: 768px) {
          max-width: 450px;
        }

        @media (max-width: 480px) {
          max-width: 350px;
        }
      }
    }
  }
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-10px);
  }
}

@keyframes slideInLeft {
  0% {
    opacity: 0;
    transform: translateX(-50px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  0% {
    opacity: 0;
    transform: translateX(50px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {

  0%,
  100% {
    transform: scale(1);
    opacity: 0.1;
  }

  50% {
    transform: scale(1.2);
    opacity: 0.2;
  }
}

// CSS Animations for Trust Indicators
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(50px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-100px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

// Feature Animation Classes
.feature-animate {
  opacity: 0;
  transform: translateX(-100px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);

  &.animate-visible {
    opacity: 1;
    transform: translateX(0);
  }
}

// Trust Indicators
.trust-indicators {
  padding: 80px 0;
  background: linear-gradient(135deg, $background-light 0%, #f0f6f9 100%);
  position: relative;
  overflow: hidden;

  // Add subtle background pattern
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpath d='M20 20c0 11.046-8.954 20-20 20s-20-8.954-20-20 8.954-20 20-20 20 8.954 20 20zm-20-18c-9.941 0-18 8.059-18 18s8.059 18 18 18 18-8.059 18-18-8.059-18-18-18z'/%3E%3C/g%3E%3C/svg%3E");
    z-index: 1;
  }

  .trust-content {
    text-align: center;
    position: relative;
    z-index: 2;

    .trust-label {
      font-size: 22px;
      color: $text-gray;
      margin-bottom: 60px;
      font-weight: 600;
      letter-spacing: 0.5px;
      text-transform: uppercase;
      position: relative;
      animation: fadeInUp 1s ease-out;

      &::after {
        content: '';
        position: absolute;
        bottom: -15px;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 3px;
        background: linear-gradient(90deg, $primary-red, transparent);
        border-radius: 2px;
      }
    }

    .awards {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 40px;
      max-width: 1000px;
      margin: 0 auto;
      align-items: stretch;

      @media (max-width: 968px) {
        grid-template-columns: 1fr;
        gap: 30px;
        max-width: 400px;
      }

      .award {
        background: $white;
        padding: 40px 25px;
        border-radius: 20px;
        box-shadow:
          0 10px 30px rgba(0, 0, 0, 0.08),
          0 4px 15px rgba(0, 0, 0, 0.05);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        border: 2px solid transparent;
        display: flex;
        flex-direction: column;
        align-items: center;
        min-height: 220px;
        justify-content: center;
        animation: slideInUp 1s ease-out;

        &.award-1 {
          animation-delay: 0.2s;
          animation-fill-mode: both;
        }

        &.award-2 {
          animation-delay: 0.4s;
          animation-fill-mode: both;
        }

        &.award-3 {
          animation-delay: 0.6s;
          animation-fill-mode: both;
        }

        // Gradient border effect
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba($primary-red, 0.1), transparent 70%);
          border-radius: 20px;
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover {
          transform: translateY(-10px) scale(1.02);
          box-shadow:
            0 20px 50px rgba(0, 0, 0, 0.15),
            0 10px 25px rgba($primary-red, 0.1);
          border-color: rgba($primary-red, 0.2);

          &::before {
            opacity: 1;
          }

          .award-badge {
            transform: scale(1.1) rotate(5deg);
          }

          .award-glow {
            opacity: 1;
            transform: scale(1.2);
          }

          .award-decoration {
            opacity: 1;
            transform: rotate(180deg);
          }
        }

        .award-icon-wrapper {
          position: relative;
          margin-bottom: 20px;

          .award-badge {
            font-size: 48px;
            display: block;
            transition: all 0.3s ease;
            position: relative;
            z-index: 2;
          }

          .award-glow {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.8);
            width: 80px;
            height: 80px;
            background: radial-gradient(circle, rgba($primary-red, 0.2), transparent 70%);
            border-radius: 50%;
            opacity: 0;
            transition: all 0.3s ease;
            z-index: 1;
          }
        }

        .award-content {
          text-align: center;
          flex-grow: 1;
          display: flex;
          flex-direction: column;
          justify-content: center;
        }

        .award-text {
          font-size: 18px;
          font-weight: 700;
          color: $dark-gray;
          margin-bottom: 12px;
          line-height: 1.4;
        }

        .award-source {
          font-size: 15px;
          color: $text-gray;
          font-weight: 500;
          opacity: 0.8;
        }

        .award-decoration {
          position: absolute;
          top: 15px;
          right: 15px;
          width: 20px;
          height: 20px;
          background: linear-gradient(45deg, $primary-red, transparent);
          border-radius: 50%;
          opacity: 0;
          transition: all 0.4s ease;
        }
      }
    }
  }
}

// Statistics
.statistics {
  padding: 80px 0;
  background-color: $dark-gray;

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
    text-align: center;

    .stat-item {
      .stat-value {
        font-size: 48px;
        font-weight: 800;
        color: $primary-red;
        margin-bottom: 8px;
        line-height: 1;

        @media (max-width: 768px) {
          font-size: 36px;
        }
      }

      .stat-label {
        font-size: 18px;
        color: $white;
        font-weight: 500;
      }
    }
  }
}

// Features Section
.features {
  padding: 120px 0;
  background: linear-gradient(135deg, $white 0%, #f8fafb 100%);
  position: relative;
  overflow: hidden;

  // Add subtle background pattern
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e31e24' fill-opacity='0.02'%3E%3Cpath d='M30 30c16.569 0 30-13.431 30-30C60 13.431 46.569 0 30 0S0 13.431 0 30c0 16.569 13.431 30 30 30z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    z-index: 1;
  }

  .container {
    position: relative;
    z-index: 2;
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 50px;
    margin-top: 80px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 40px;
      margin-top: 60px;
    }

    .feature-card {
      background: $white;
      border-radius: 24px;
      overflow: hidden;
      box-shadow:
        0 15px 35px rgba(0, 0, 0, 0.08),
        0 5px 15px rgba(0, 0, 0, 0.05);
      transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
      border: 2px solid transparent;
      position: relative;

      &:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow:
          0 25px 50px rgba(0, 0, 0, 0.15),
          0 10px 25px rgba($primary-red, 0.1);
        border-color: rgba($primary-red, 0.2);

        .feature-image {
          transform: scale(1.1);
        }

        .feature-overlay {
          opacity: 1;
        }

        .feature-icon {
          transform: scale(1.2) rotate(10deg);
        }
      }

      .feature-image-container {
        position: relative;
        height: 200px;
        overflow: hidden;

        .feature-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.5s ease;
        }

        .feature-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba($primary-red, 0.8), rgba($primary-red, 0.6));
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s ease;

          .feature-icon {
            font-size: 64px;
            color: $white;
            transition: all 0.3s ease;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
          }
        }
      }

      .feature-content {
        padding: 30px 25px;
        text-align: center;

        .feature-title {
          font-size: 24px;
          font-weight: 700;
          color: $dark-gray;
          margin-bottom: 16px;
          line-height: 1.3;
        }

        .feature-description {
          font-size: 16px;
          color: $text-gray;
          line-height: 1.6;
          margin: 0;
        }
      }
    }
  }
}

// Coverage Control Section
.coverage-control {
  padding: 100px 0;
  background-color: $background-light;

  .coverage-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;

    @media (max-width: 968px) {
      grid-template-columns: 1fr;
      gap: 40px;
      text-align: center;
    }

    .coverage-text {
      .coverage-title {
        font-size: 42px;
        font-weight: 700;
        color: $dark-gray;
        margin-bottom: 24px;
        line-height: 1.2;

        @media (max-width: 768px) {
          font-size: 32px;
        }
      }

      .coverage-description {
        font-size: 20px;
        color: $text-gray;
        line-height: 1.6;
        margin-bottom: 32px;

        @media (max-width: 768px) {
          font-size: 18px;
        }
      }
    }

    .coverage-visual {
      display: flex;
      flex-direction: column;
      align-items: center;

      .coverage-chart {
        display: flex;
        align-items: end;
        gap: 20px;
        height: 200px;
        margin-bottom: 20px;

        .chart-bar {
          width: 60px;
          background: linear-gradient(to top, $primary-red, lighten($primary-red, 20%));
          border-radius: 8px 8px 0 0;
          position: relative;
          transition: all 0.3s ease;

          &:hover {
            transform: scale(1.05);
          }

          .chart-label {
            position: absolute;
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 14px;
            font-weight: 600;
            color: $dark-gray;
            white-space: nowrap;
          }
        }
      }

      .chart-note {
        font-size: 16px;
        color: $text-gray;
        text-align: center;
        font-style: italic;
      }
    }
  }
}

// FAQ Section
.faq {
  padding: 100px 0;
  background-color: $white;

  .faq-list {
    max-width: 800px;
    margin: 0 auto;

    .faq-item {
      border-bottom: 1px solid $border-gray;
      margin-bottom: 0;

      &:last-child {
        border-bottom: none;
      }

      .faq-question {
        width: 100%;
        padding: 24px 0;
        background: none;
        border: none;
        text-align: left;
        font-size: 20px;
        font-weight: 600;
        color: $dark-gray;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        transition: color 0.3s ease;

        &:hover {
          color: $primary-red;
        }

        .faq-toggle {
          font-size: 24px;
          font-weight: 300;
          transition: transform 0.3s ease;
          color: $primary-red;

          &.rotated {
            transform: rotate(45deg);
          }
        }
      }

      .faq-answer {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease, padding 0.3s ease;

        &.open {
          max-height: 200px;
          padding-bottom: 24px;
        }

        p {
          font-size: 16px;
          color: $text-gray;
          line-height: 1.6;
          margin: 0;
        }
      }
    }
  }
}

// Final CTA Section
.final-cta {
  padding: 100px 0;
  background: linear-gradient(135deg, $primary-red 0%, darken($primary-red, 15%) 100%);
  color: $white;

  .cta-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;

    @media (max-width: 968px) {
      grid-template-columns: 1fr;
      gap: 40px;
      text-align: center;
    }

    .cta-text {
      .cta-title {
        font-size: 42px;
        font-weight: 700;
        color: $white;
        margin-bottom: 24px;
        line-height: 1.2;

        @media (max-width: 768px) {
          font-size: 32px;
        }
      }

      .cta-description {
        font-size: 20px;
        color: rgba($white, 0.9);
        line-height: 1.6;
        margin-bottom: 32px;

        @media (max-width: 768px) {
          font-size: 18px;
        }
      }

      .btn-primary {
        background-color: $white;
        color: $primary-red;
        border-color: $white;

        &:hover {
          background-color: $light-gray;
          border-color: $light-gray;
          color: $primary-red;
        }
      }
    }

    .cta-image {
      display: flex;
      justify-content: center;
      align-items: center;

      .family-image {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;

        .family-icon {
          font-size: 120px;
          opacity: 0.9;
        }

        .protection-shield {
          position: absolute;
          top: -20px;
          right: -20px;
          font-size: 60px;
          background: rgba($white, 0.2);
          border-radius: 50%;
          padding: 10px;
          animation: pulse 2s infinite;
        }
      }
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.1);
  }

  100% {
    transform: scale(1);
  }
}