# Firebase Setup Instructions

## 1. Create a Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project" or "Add project"
3. Enter project name (e.g., "surevie-app")
4. Enable Google Analytics (optional)
5. Click "Create project"

## 2. Enable Authentication

1. In your Firebase project, go to "Authentication" in the left sidebar
2. Click "Get started"
3. Go to "Sign-in method" tab
4. Enable "Email/Password" provider
5. Click "Save"

## 3. Enable Firestore Database

1. Go to "Firestore Database" in the left sidebar
2. Click "Create database"
3. Choose "Start in test mode" (for development)
4. Select a location for your database
5. Click "Done"

## 4. Get Firebase Configuration

1. Go to "Project settings" (gear icon in left sidebar)
2. Scroll down to "Your apps" section
3. Click "Web" icon (</>) to add a web app
4. Enter app nickname (e.g., "SureVie Web")
5. Click "Register app"
6. Copy the Firebase configuration object

## 5. Update Environment Files

Replace the placeholder values in these files with your actual Firebase config:

### `src/environments/environment.ts`
```typescript
export const environment = {
  production: false,
  firebase: {
    apiKey: "your-actual-api-key",
    authDomain: "your-project-id.firebaseapp.com",
    projectId: "your-actual-project-id",
    storageBucket: "your-project-id.appspot.com",
    messagingSenderId: "your-actual-sender-id",
    appId: "your-actual-app-id",
    measurementId: "your-measurement-id" // Optional
  }
};
```

### `src/environments/environment.prod.ts`
```typescript
export const environment = {
  production: true,
  firebase: {
    // Same configuration as above
  }
};
```

## 6. Test the Integration

1. Run `ng serve` to start the development server
2. Navigate to the registration page
3. Try creating a new user account
4. Check Firebase Console > Authentication to see if the user was created
5. Check Firestore Database to see if user profile was stored

## 7. Firestore Security Rules (Optional for Development)

For development, you can use these basic rules in Firestore:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own profile
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Contracts can be read/written by authenticated users
    match /contracts/{contractId} {
      allow read, write: if request.auth != null;
    }
  }
}
```

## 8. Features Implemented

- ✅ Firebase Authentication (Email/Password)
- ✅ User registration with role-based profiles
- ✅ User login with error handling
- ✅ Firestore integration for user profiles
- ✅ Authentication state management
- ✅ Role-based routing protection
- ✅ Firestore service for insurance contracts

## Next Steps

1. Set up your Firebase project following steps 1-5
2. Update the environment files with your Firebase config
3. Test the authentication flow
4. Customize Firestore security rules for production
5. Add more features like password reset, email verification, etc.
