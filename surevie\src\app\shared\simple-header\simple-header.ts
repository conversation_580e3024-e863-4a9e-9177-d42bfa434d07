import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';

@Component({
  selector: 'app-simple-header',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './simple-header.html',
  styleUrls: ['./simple-header.scss']
})
export class SimpleHeader {
  isMobileMenuOpen = false;

  constructor(private router: Router) { }

  navigateToLogin(): void {
    this.router.navigate(['/auth/login']);
    this.closeMobileMenu();
  }

  navigateToRegister(): void {
    this.router.navigate(['/auth/register']);
    this.closeMobileMenu();
  }

  navigateToHome(): void {
    this.router.navigate(['/']);
    this.closeMobileMenu();
  }

  toggleMobileMenu(): void {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
  }

  closeMobileMenu(): void {
    this.isMobileMenuOpen = false;
  }
}
