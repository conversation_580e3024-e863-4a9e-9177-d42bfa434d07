import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './home.html',
  styleUrls: ['./home.scss']
})
export class Home {

  // FAQ data
  faqs = [
    {
      question: 'Will <PERSON><PERSON><PERSON> be around to pay my claim?',
      answer: 'Our policies are backed by highly rated insurers with strong financial stability. We are committed to being here for you and your family when you need us most.',
      isOpen: false
    },
    {
      question: 'Money back guarantee?',
      answer: 'Yes! If you\'re not completely satisfied with your policy, cancel within the first 30 days for a full refund. After that, you can cancel anytime without cancellation fees.',
      isOpen: false
    },
    {
      question: 'Is SureVie 100% digital?',
      answer: 'We offer a streamlined digital experience for most applications. For coverage up to $2 million, our process is completely digital with no medical exams required.',
      isOpen: false
    },
    {
      question: 'Why term life insurance vs whole life insurance?',
      answer: 'Term insurance provides affordable protection during the years your family depends on you most. It\'s typically more cost-effective than whole life insurance, allowing you to get more coverage for less.',
      isOpen: false
    },
    {
      question: 'How much life insurance do I need?',
      answer: '10x your annual salary is a good starting point. Consider your mortgage, debts, children\'s education costs, and your family\'s ongoing expenses. Use our coverage calculator for a personalized recommendation.',
      isOpen: false
    },
    {
      question: 'Who can I talk to if I need help?',
      answer: 'Our expert customer service team is available to help you understand your options and find the right policy. Contact us anytime for personalized assistance.',
      isOpen: false
    }
  ];

  // Statistics
  stats = [
    { value: '$50B+', label: 'Coverage Provided' },
    { value: '100K+', label: 'Families Protected' },
    { value: '4.9/5', label: 'Customer Rating' },
    { value: '24/7', label: 'Support Available' }
  ];

  // Features
  features = [
    {
      icon: '⚡',
      title: 'Quick Application',
      description: 'Get a quote and apply in minutes with our streamlined digital process.'
    },
    {
      icon: '🛡️',
      title: 'Comprehensive Coverage',
      description: 'Protect your family with flexible term life insurance options from $100K to $5M.'
    },
    {
      icon: '💰',
      title: 'Affordable Rates',
      description: 'Competitive pricing starting as low as $15/month for healthy applicants.'
    },
    {
      icon: '📱',
      title: 'Digital First',
      description: 'Manage your policy, make payments, and file claims all from your smartphone.'
    }
  ];

  constructor(private router: Router) { }

  // Navigation methods
  getStarted() {
    this.router.navigate(['/auth/register']);
  }

  getQuote() {
    this.router.navigate(['/auth/register']);
  }

  learnMore() {
    // Scroll to features section
    const featuresSection = document.getElementById('features');
    if (featuresSection) {
      featuresSection.scrollIntoView({ behavior: 'smooth' });
    }
  }

  // FAQ toggle
  toggleFaq(index: number) {
    this.faqs[index].isOpen = !this.faqs[index].isOpen;
  }

  // Scroll to section
  scrollToSection(sectionId: string) {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  }
}
