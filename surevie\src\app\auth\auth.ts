import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { BehaviorSubject, Observable, of, from } from 'rxjs';
import { delay, map, catchError, switchMap } from 'rxjs/operators';
import { Auth, signInWithEmailAndPassword, createUserWithEmailAndPassword, signOut, authState, User as FirebaseUser, updateProfile } from '@angular/fire/auth';
import { Firestore, doc, setDoc, getDoc, docData } from '@angular/fire/firestore';

export interface User {
  uid: string;
  email: string;
  displayName: string;
  role: 'admin' | 'broker' | 'client';
  createdAt: Date;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  displayName: string;
  role: 'broker' | 'client';
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);

  public currentUser$ = this.currentUserSubject.asObservable();
  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();

  constructor(
    @Inject(PLATFORM_ID) private platformId: Object,
    private auth: Auth,
    private firestore: Firestore
  ) {
    // Listen to Firebase auth state changes
    this.initAuthStateListener();
  }

  private initAuthStateListener(): void {
    // Listen to Firebase auth state changes
    authState(this.auth).subscribe(async (firebaseUser: FirebaseUser | null) => {
      if (firebaseUser) {
        // User is signed in, get additional user data from Firestore
        const userDoc = await this.getUserProfile(firebaseUser.uid);
        if (userDoc) {
          this.currentUserSubject.next(userDoc);
          this.isAuthenticatedSubject.next(true);
        }
      } else {
        // User is signed out
        this.currentUserSubject.next(null);
        this.isAuthenticatedSubject.next(false);
      }
    });
  }

  login(credentials: LoginCredentials): Observable<{ success: boolean; user?: User; error?: string }> {
    return from(signInWithEmailAndPassword(this.auth, credentials.email, credentials.password)).pipe(
      switchMap(async (userCredential) => {
        // Get user profile from Firestore
        const userProfile = await this.getUserProfile(userCredential.user.uid);
        if (userProfile) {
          return { success: true, user: userProfile };
        } else {
          return { success: false, error: 'User profile not found' };
        }
      }),
      catchError((error) => {
        let errorMessage = 'Login failed';
        switch (error.code) {
          case 'auth/user-not-found':
            errorMessage = 'No user found with this email';
            break;
          case 'auth/wrong-password':
            errorMessage = 'Incorrect password';
            break;
          case 'auth/invalid-email':
            errorMessage = 'Invalid email address';
            break;
          case 'auth/too-many-requests':
            errorMessage = 'Too many failed attempts. Please try again later';
            break;
          default:
            errorMessage = error.message || 'Login failed';
        }
        return of({ success: false, error: errorMessage });
      })
    );
  }

  register(userData: RegisterData): Observable<{ success: boolean; user?: User; error?: string }> {
    return from(createUserWithEmailAndPassword(this.auth, userData.email, userData.password)).pipe(
      switchMap(async (userCredential) => {
        try {
          // Update the user's display name
          await updateProfile(userCredential.user, {
            displayName: userData.displayName
          });

          // Create user profile in Firestore
          const newUser: User = {
            uid: userCredential.user.uid,
            email: userData.email,
            displayName: userData.displayName,
            role: userData.role,
            createdAt: new Date()
          };

          await this.createUserProfile(newUser);
          return { success: true, user: newUser };
        } catch (error) {
          return { success: false, error: 'Failed to create user profile' };
        }
      }),
      catchError((error) => {
        let errorMessage = 'Registration failed';
        switch (error.code) {
          case 'auth/email-already-in-use':
            errorMessage = 'An account with this email already exists';
            break;
          case 'auth/invalid-email':
            errorMessage = 'Invalid email address';
            break;
          case 'auth/weak-password':
            errorMessage = 'Password should be at least 6 characters';
            break;
          default:
            errorMessage = error.message || 'Registration failed';
        }
        return of({ success: false, error: errorMessage });
      })
    );
  }

  logout(): Observable<void> {
    return from(signOut(this.auth));
  }

  getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  isAuthenticated(): boolean {
    return this.isAuthenticatedSubject.value;
  }

  hasRole(role: string): boolean {
    const user = this.getCurrentUser();
    return user ? user.role === role : false;
  }

  // Helper method to create user profile in Firestore
  private async createUserProfile(user: User): Promise<void> {
    const userRef = doc(this.firestore, `users/${user.uid}`);
    await setDoc(userRef, {
      email: user.email,
      displayName: user.displayName,
      role: user.role,
      createdAt: user.createdAt
    });
  }

  // Helper method to get user profile from Firestore
  private async getUserProfile(uid: string): Promise<User | null> {
    try {
      const userRef = doc(this.firestore, `users/${uid}`);
      const userSnap = await getDoc(userRef);

      if (userSnap.exists()) {
        const data = userSnap.data();
        return {
          uid: uid,
          email: data['email'],
          displayName: data['displayName'],
          role: data['role'],
          createdAt: data['createdAt']?.toDate() || new Date()
        };
      }
      return null;
    } catch (error) {
      console.error('Error getting user profile:', error);
      return null;
    }
  }
}
